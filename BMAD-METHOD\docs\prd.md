# **Ollama-to-OpenAI API Bridge Product Requirements Document (PRD)**

## **1. Goals and Background Context**

### **1.1. Goals**

- **Seamless Translation:** Successfully translate Ollama's /api/chat and /api/tags endpoint requests and responses to be fully compatible with an OpenAI-compatible backend service (/v1/chat/completions and /v1/models respectively). <sup>11</sup>

- **Streaming Parity:** Ensure real-time, token-by-token streaming from the backend service is correctly handled and passed through to the client, mimicking native Ollama streaming behavior.

- **Configuration Flexibility:** Enable the bridge to be easily configured with a target backend endpoint URL and the necessary API key for secure communication. <sup>2</sup>

- **Dynamic Model-Mapping:** Implement a robust configuration system that allows for mapping client-requested Ollama model names (e.g., llama3) to specific backend model IDs (e.g., gpt-4o-mini).


### **1.2. Background Context**

The proliferation of Large Language Models (LLMs) has created a diverse ecosystem, but a significant interoperability gap exists between locally-run open-source models via Ollama and proprietary cloud APIs like OpenAI. <sup>3</sup> Developers building applications for one platform often face substantial code modification when needing to switch to the other due to differing API formats. This project aims to close that gap.

This PRD outlines the requirements for a server-side translation bridge. It will act as a middleware that intercepts Ollama-formatted API calls, translates them into the OpenAI-compatible format for a backend service, and then translates the responses back for the client. <sup>4</sup> This will enable applications to switch between local and cloud LLMs without any client-side code changes, providing ultimate flexibility in development and deployment. <sup>5</sup>


## **2. Requirements**

### **2.1. Functional Requirements**

- **FR1:** The service must provide an endpoint GET /health for health checks.

- **FR2:** The service must translate requests from Ollama's POST /api/chat to a backend's POST /v1/chat/completions, including support for both streaming and non-streaming responses.

- **FR3:** The service must translate responses from a backend's GET /v1/models to Ollama's GET /api/tags format.

- **FR4:** The service must expose an endpoint GET /v1/models to list available models from the backend.

- **FR5:** The service must expose an endpoint GET /v1/TTI/models to list available Text-to-Image models.

- **FR6:** The service must expose an endpoint POST /v1/images/generations to handle image generation requests.

- **FR7:** The service must expose an endpoint POST /v1/auth/generate-key to generate API keys.

- **FR8:** The service must expose an endpoint GET /v1/auth/validate to validate an API key.

- **FR9:** The service must expose an endpoint GET /search to perform a web search.


### **2.2. Non-Functional Requirements**

- **NFR1:** The bridge must be configurable with the backend service's URL and a security token/API key.

- **NFR2:** The service must include a configurable mapping system to translate Ollama model names to backend-specific model IDs.

- **NFR3:** The latency overhead for non-streaming requests should be minimal, targeting an average of less than 100ms.

- **NFR4:** The service must not require authentication for incoming client requests, mirroring Ollama's default behavior. <sup>6</sup>

- **NFR5:** The application will be developed using Python and the FastAPI framework.


## **3. Technical Assumptions**

- **Primary Language:** Python (Version 3.11 or higher)

- **Primary Framework:** FastAPI

- **Testing requirements:** The testing strategy will include Unit Tests for translation logic, Integration Tests for endpoint functionality, and End-to-End Tests to validate key user flows against a test backend. <sup>7</sup>

- **Repository Structure:** A dedicated Polyrepo (a single repository for this service). <sup>8</sup>

- **Service Architecture:** A Monolithic Service architecture (a single, deployable application). <sup>9</sup>


## **4. Epics**

### **Epic 1: Foundational Bridge & Core Translation Service**

**Goal:** Establish the core FastAPI application, implement the primary Ollama-to-OpenAI translation for chat and model listing, and include all necessary configurations to create a testable, value-delivering product. <sup>10</sup>

- **Story 1.1: Project Setup & Health Check**

* As a developer, I want a basic FastAPI application structure with a /health endpoint, so that I have a verifiable foundation to build upon and can monitor the service's status. <sup>11</sup>

- **Story 1.2: Implement Configuration System**

* As a developer, I want to configure the bridge with a backend URL, API key, and model mappings from environment variables, so that I can easily deploy and manage the service without changing code.

- **Story 1.3: Non-Streaming Chat Translation**

* As a user, I want to send a non-streaming Ollama-formatted chat request to the bridge, so that it gets translated and forwarded to the OpenAI-compatible backend, and the response is translated back to me. <sup>12</sup>

- **Story 1.4: Streaming Chat Translation**

* As a user, I want to send a streaming Ollama-formatted chat request to the bridge, so that I receive the response token-by-token in real-time.

- **Story 1.5: Model List Translation**

* As a user, I want to request the model list from the bridge in the Ollama format, so that my client application can display the available models from the backend.

- **Story 1.6: Implement Model Name Mapping**

* As a user, I want the bridge to use a configured map to translate model names, so that when I request a generic model name, it is sent to the backend as the specific required model.


### **Epic 2: Expanded API Capabilities**

**Goal:** Build upon the foundational bridge by adding new, direct endpoints for model listing, image generation, and web search, transforming the service into a more comprehensive AI gateway. <sup>13</sup>

- **Story 2.1: Expose Backend Model List Directly**

* As a developer, I want to call a /v1/models endpoint on the bridge, so that I can get a direct, unfiltered list of models available from the backend service.

- **Story 2.2: List Text-to-Image Models**

* As a developer, I want a dedicated /v1/TTI/models endpoint, so that I can easily discover which models are for text-to-image generation.

- **Story 2.3 (Revised): Implement Image Generation Endpoint**

* As a user, I want to send a detailed request to POST /v1/images/generations, so that I can generate an image with specific parameters.

- **Story 2.4 (Revised): Implement Comprehensive Web Search Endpoint**

* As a user, I want to call a GET /search endpoint with detailed parameters, so that I can get tailored web search results.


### **Epic 3: Authentication Management Service**

**Goal:** To build a self-contained service within the bridge for generating and validating API keys, enabling a path to secure specific endpoints as needed.

- **Story 3.1: Design and Implement Secure Key Storage**

* As a developer, I want a secure mechanism to store API keys, so that keys are not exposed in plaintext.

- **Story 3.2: Implement Key Generation Endpoint**

* As a developer, I want to call the POST /v1/auth/generate-key endpoint, so that I can create a new, unique API key.

- **Story 3.3: Implement Key Validation Endpoint**

* As a developer, I want to call the GET /v1/auth/validate endpoint with a key, so that I can verify if it is valid.